# Dify代码节点示例 - 禁用SSL验证的HTTP请求

def main(begin_time: str, end_time: str, code: str) -> dict:
    """
    Dify代码节点主函数
    
    输入变量:
    - begin_time: 开始时间
    - end_time: 结束时间  
    - code: 代码标识
    
    输出:
    - result: API响应结果
    """
    import requests
    import json
    import urllib3
    
    # 禁用SSL警告
    urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
    
    # API配置
    url = "https://172.22.147.144:19000/eteintegration/eteworkbenchgather/look/forZhiXing/indicatorDataList"
    
    headers = {
        "Content-Type": "application/json"
    }
    
    data = {
        "beginTime": begin_time,
        "endTime": end_time,
        "code": code
    }
    
    try:
        # 发送请求，禁用SSL验证
        response = requests.post(
            url=url,
            headers=headers,
            json=data,
            verify=False,  # 关键：禁用SSL证书验证
            timeout=30
        )
        
        response.raise_for_status()
        result = response.json()
        
        return {
            "success": True,
            "data": result,
            "status_code": response.status_code
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "data": None
        }
