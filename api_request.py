#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
API请求示例 - 将curl命令转换为Python requests实现
兼容Python 2.7
"""

import requests
import json


def make_api_request(
    begin_time="2025-07-22 02:13:47",
    end_time="2025-07-22 02:23:47",
    code="ywpt_smscenter_bc_jtwg"
):
    """
    发送API请求获取指标数据

    Args:
        begin_time: 开始时间
        end_time: 结束时间
        code: 代码标识

    Returns:
        API响应数据，如果请求失败返回None
    """
    # API端点URL
    url = "https://172.22.147.144:19000/eteintegration/eteworkbenchgather/look/forZhiXing/indicatorDataList"

    # 请求头
    headers = {
        "Content-Type": "application/json"
    }

    # 请求数据
    data = {
        "beginTime": begin_time,
        "endTime": end_time,
        "code": code
    }

    try:
        # 发送POST请求，禁用SSL验证（对应curl的-k参数）
        # Python 2.7的requests版本可能不支持json参数，使用data参数
        response = requests.post(
            url=url,
            headers=headers,
            data=json.dumps(data),  # 手动序列化JSON数据
            verify=False,  # 对应curl的-k参数，禁用SSL证书验证
            timeout=30  # 设置超时时间
        )

        # 检查响应状态
        response.raise_for_status()

        # 返回JSON响应
        return response.json()

    except requests.exceptions.RequestException as e:
        print("请求失败: {}".format(e))
        return None
    except ValueError as e:  # Python 2.7中json.JSONDecodeError不存在，使用ValueError
        print("JSON解析失败: {}".format(e))
        print("响应内容: {}".format(response.text))
        return None


def main():
    """主函数示例"""
    # 使用默认参数发送请求
    result = make_api_request()

    if result:
        print("请求成功!")
        print("响应数据: {}".format(json.dumps(result, indent=2, ensure_ascii=False)))
    else:
        print("请求失败!")

    # 使用自定义参数发送请求
    custom_result = make_api_request(
        begin_time="2025-07-30 10:00:00",
        end_time="2025-07-30 11:00:00",
        code="custom_code"
    )

    if custom_result:
        print("\n自定义参数请求成功!")
        print("响应数据: {}".format(json.dumps(custom_result, indent=2, ensure_ascii=False)))


if __name__ == "__main__":
    # 禁用SSL警告（因为使用了verify=False）
    import urllib3
    urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

    main()
