identity:
  author: "qzh"
  name: "api-call"
  label:
    en_US: "api-call"
    zh_<PERSON>: "api-call"
    pt_BR: "api-call"
  description:
    en_US: "通过解析openapi3.1规范性文档调用api接口"
    zh_Hans: "通过解析openapi3.1规范性文档调用api接口"
    pt_BR: "通过解析openapi3.1规范性文档调用api接口"
  icon: "icon.svg"
tools:
  - tools/plugins_demo.yaml
extra:
  python:
    source: provider/plugins_demo.py
credentials_for_provider:
    api_call_access_token: # 这是凭证的内部名称，将在 Python 代码中使用
        type: secret-input # 输入类型为密码框
        required: false # 此凭证是必需的
        label: # 在 Dify UI 中显示的标签 (支持多语言)
            en_US: api-call Access Token
            zh_Hans: api-call 访问令牌
            # ... (其他语言)
        placeholder: # 输入框的提示文字 (支持多语言)
            en_US: Enter your api-call access token
            zh_Hans: 请输入您的 api-call 访问令牌
            # ... (其他语言)
        help: # 帮助提示信息 (支持多语言)
            en_US: How to get your api-call access token
            zh_Hans: 如何获取 api-call 访问令牌
            # ... (其他语言)
