from typing import Any

from dify_plugin import <PERSON><PERSON><PERSON>rovider
from dify_plugin.errors.tool import ToolProviderCredentialValidationError

import logging
from dify_plugin.config.logger_format import plugin_logger_handler

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)
logger.addHandler(plugin_logger_handler)

class ApiCallProvider(ToolProvider):
    def _validate_credentials(self, credentials: dict[str, Any]) -> None:
        token = credentials.get("api_call_access_token", "no-token")
        print(f"接收到凭证: {token}")