identity:
  name: "API-CALL"
  display_name: "通过解析openapi3.1规范性文档调用api接口"
  author: "qzh"
  label:
    en_US: "api接口调用"
    zh_Hans: "api接口调用"
description:
  human:
    en_US: "根据选择的选项执行不同api接口调用操作"
    zh_Hans: "根据选择的选项执行不同api接口调用操作"
  llm: "根据P_CHOICE参数执行相应操作"
parameters:
  - name: "P_CHOICE"
    required: true
    label:
      en_US: "执行选项"
      zh_Hans: "执行选项"
    human_description:
      en_US: "选择要执行的操作选项"
      zh_Hans: "选择要执行的操作选项"
    # 关键修复：严格遵循官方文档定义
    type: "select"  # 官方规范要求下拉框类型为select[2](@ref)
    form: "form"    # 兼容旧版SDK校验
    options: 
      - value: "1"
        label:
          en_US: "选项 1"
          zh_Hans: "选项 1"
      - value: "2"
        label:
          en_US: "选项 2"
          zh_Hans: "选项 2"
      - value: "3"
        label:
          en_US: "选项 3"
          zh_Hans: "选项 3"
output:  
  - name: "result"
    type: string
    label:
      en_US: "执行结果"
      zh_Hans: "执行结果"
extra:
  python:
    source: tools/plugins_demo.py