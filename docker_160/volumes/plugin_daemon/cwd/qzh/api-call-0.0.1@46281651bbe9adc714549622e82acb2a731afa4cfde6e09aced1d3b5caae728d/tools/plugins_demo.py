from collections.abc import Generator
from typing import Any
import json
import os
from dify_plugin import Tool
from dify_plugin.entities.tool import ToolInvokeMessage
from .parse_yaml import APIClient
from .logging import logger

class ApiCallProviderTool(Tool):
    def _invoke(self, tool_parameters: dict[str, Any]) -> Generator[ToolInvokeMessage]:
        # 安全获取凭证（无凭证时使用默认值）
        access_token = self.runtime.credentials.get("api_call_access_token", "default-token")
        logger.info(f"使用凭证: {access_token}")

        # 获取用户选择的选项
        choice = tool_parameters.get("P_CHOICE", "1")
        logger.info(f"输入的选项是{choice}")
        
        # 修复路径：获取当前文件的父目录（即tools的上级目录）[7,8](@ref)
        current_dir = os.path.dirname(os.path.abspath(__file__))
        parent_dir = os.path.dirname(current_dir)  # 获取tools的上级目录（plugins-demo）
        yaml_path = os.path.join(parent_dir, "openapi3", "generate_sql.yaml")  # 正确路径
        
        logger.info(f"修正后的yaml文件路径是：{yaml_path}")

        # 根据选项执行不同的操作
        if choice == "1":
            logger.info("选择1")
            client = APIClient(yaml_path)
            result_data = client.execute()
            result = json.dumps(result_data, ensure_ascii=False)
        elif choice == "2":
            logger.info("选择2")
            result = "执行了选项2的操作"
        elif choice == "3":
            logger.info("选择3")
            result = "执行了选项3的操作"
        else:
            result = f"错误: 不支持的操作选项 '{choice}'"
        
        # 返回执行结果
        yield self.create_text_message(result)