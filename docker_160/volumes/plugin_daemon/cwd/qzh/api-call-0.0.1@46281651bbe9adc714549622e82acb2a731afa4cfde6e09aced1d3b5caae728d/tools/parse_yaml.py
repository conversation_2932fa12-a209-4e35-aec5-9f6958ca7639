import yaml
import requests
import os
from typing import Dict, Any, List, Optional
from urllib.parse import urljoin
from .logging import logger

class APIClient:
    def __init__(self, yaml_path: str):
        """
        加载并解析API YAML规范，自动提取默认参数值
        :param yaml_path: YAML文件路径
        """
        logger.info("检查文件")
        if not os.path.exists(yaml_path):
            logger.error(f"YAML文件不存在{yaml_path}")
            raise FileNotFoundError(f"YAML文件不存在: {yaml_path}")
        
        self.spec = self._load_and_parse_spec(yaml_path)
        self.base_url = self.spec["servers"][0]["url"]
        self.session = requests.Session()
        self.session.headers.update({"Content-Type": "application/json"})
        
        # 解析API端点信息和参数默认值
        self.api_path, self.http_method, self.parameters = self._parse_api_endpoint()
        logger.info(f"api_path:{self.api_path}")
        logger.info(f"http_method:{self.http_method}")
        logger.info(f"parameters:{self.parameters}")
        self.default_params = self._extract_default_parameters()
        logger.info(f"default_params:{self.default_params}")

    def _load_and_parse_spec(self, path: str) -> Dict[str, Any]:
        """解析YAML文件"""
        logger.info("加载文件")
        try:
            with open(path, "r", encoding="utf-8") as f:
                return yaml.safe_load(f)
        except Exception as e:
            raise ValueError(f"YAML解析失败: {e}")

    def _parse_api_endpoint(self) -> tuple:
        """解析唯一的API端点及其参数定义"""
        logger.info("解析参数")
        paths = self.spec.get("paths", {})
        if not paths:
            raise ValueError("YAML中没有定义任何API路径")
        
        path_name = next(iter(paths.keys()))
        path_item = paths[path_name]
        
        # 获取支持的HTTP方法
        methods = [m for m in ["get", "post", "put", "delete", "patch"] if m in path_item]
        if not methods:
            raise ValueError("未找到支持的HTTP方法")
        
        http_method = methods[0]
        operation = path_item[http_method]
        
        # 提取参数定义
        parameters = operation.get("parameters", [])
        
        return path_name, http_method, parameters

    def _extract_default_parameters(self) -> Dict[str, Any]:
        """从参数定义中提取默认值"""
        default_params = {}
        for param in self.parameters:
            param_name = param["name"]
            schema = param.get("schema", {})
            
            if "default" in schema:
                default_params[param_name] = schema["default"]
        
        return default_params

    def execute(
        self,
        path_params: Optional[Dict[str, Any]] = None,
        query_params: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        执行API请求，自动填充默认参数值
        :return: 响应数据 (JSON格式)
        """
        logger.info("开始执行")
        # 合并用户参数和默认参数（用户参数优先）
        final_query_params = {**self.default_params, **(query_params or {})}
        
        # 验证必需参数
        required_params = [
            p["name"] for p in self.parameters 
            if p.get("required", False)
        ]
        
        missing_params = [
            p for p in required_params 
            if p not in final_query_params
        ]
        if missing_params:
            raise ValueError(f"缺少必需的参数: {', '.join(missing_params)}")

        # 构造完整URL
        if path_params:
            url = self.base_url + self.api_path.format(**path_params)
        else:
            url = urljoin(self.base_url, self.api_path)

        logger.info(f"url:{url}")
        logger.info(f"final_query_params:{final_query_params}")

        # 发送请求
        try:
            response = self.session.request(
                method=self.http_method,
                url=url,
                params=final_query_params
            )
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            error_detail = f"{e}"
            if e.response is not None:
                try:
                    error_detail += f" | 响应内容: {e.response.text[:200]}"
                except:
                    pass
            raise ConnectionError(f"API请求失败: {error_detail}") from e
