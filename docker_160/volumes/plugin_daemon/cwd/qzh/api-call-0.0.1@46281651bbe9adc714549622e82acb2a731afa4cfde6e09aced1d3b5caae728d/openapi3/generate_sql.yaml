openapi: 3.1.0
info:
  title: SQL生成服务API
  description: 根据自然语言问题生成SQL查询语句的服务
  version: 1.0.0

servers:
  - url: http://*************:8882
    description: 生产环境

paths:
  /api/v0/generate_sql:
    get:
      operationId: generateSQL
      summary: 根据自然语言问题生成SQL语句
      description: |
        接收自然语言描述的问题，返回对应的SQL查询语句。
        支持重试机制（最多3次）和SQL标准化处理。
      parameters:
        - name: question
          in: query
          required: true
          description: 自然语言描述的问题文本
          schema:
            type: string
            default: "帮忙看下这个订单702023032309240136986对应的退费是谁创建的"
        - name: limit
          in: query
          required: false
          description: 返回结果数量限制
          schema:
            type: integer
            default: 10
      responses:
        '200':
          description: 成功响应
          content:
            application/json:
              schema:
                type: object
                properties:
                  text:
                    type: string
                    description: 生成的SQL查询语句
                    example: "SELECT * FROM sales WHERE date >= CURRENT_DATE - INTERVAL '1 month'"
        '400':
          description: 请求参数无效
        '500':
          description: 服务器内部错误